package models

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/util"
)

// ConnectionService provides high-level operations for connection management
type ConnectionService struct {
	queries *db.Queries
	encKey  util.EncryptionKey
}

// NewConnectionService creates a new connection service
func NewConnectionService(queries *db.Queries) *ConnectionService {
	return &ConnectionService{
		queries: queries,
		encKey:  util.GetEncryptionKey(),
	}
}

// CreateConnectionRequest represents a request to create a new connection
type CreateConnectionRequest struct {
	Name        string
	Description string
	BaseURL     string
	Auth        util.ConnectionAuth
}

// UpdateConnectionRequest represents a request to update a connection
type UpdateConnectionRequest struct {
	Name        *string
	Description *string
	BaseURL     *string
	Auth        *util.ConnectionAuth
}

// ConnectionInfo represents connection information with decrypted auth details
type ConnectionInfo struct {
	ID          int64
	Name        string
	Description string
	BaseURL     string
	Auth        util.ConnectionAuth
	CreatedAt   pgtype.Timestamptz
	UpdatedAt   pgtype.Timestamptz
}

// CreateConnection creates a new connection with encrypted auth details
func (cs *ConnectionService) CreateConnection(ctx context.Context, req CreateConnectionRequest) (*ConnectionInfo, error) {
	// Validate auth type
	if !isValidAuthType(req.Auth.Type) {
		return nil, fmt.Errorf("invalid auth type: %s", req.Auth.Type)
	}

	// Encrypt sensitive auth data
	encryptedAuth, err := cs.encKey.EncryptConnectionAuth(&req.Auth)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt auth data: %w", err)
	}

	// Prepare database parameters
	params := db.CreateConnectionParams{
		Name:    req.Name,
		BaseUrl: req.BaseURL,
		AuthType: req.Auth.Type,
	}

	if req.Description != "" {
		params.Description = pgtype.Text{String: req.Description, Valid: true}
	}

	if req.Auth.Username != "" {
		params.AuthUsername = pgtype.Text{String: req.Auth.Username, Valid: true}
	}

	if req.Auth.HeaderName != "" {
		params.AuthHeaderName = pgtype.Text{String: req.Auth.HeaderName, Valid: true}
	}

	if passwordEnc, ok := encryptedAuth["password"]; ok {
		params.AuthPasswordEncrypted = passwordEnc
	}

	if tokenEnc, ok := encryptedAuth["token"]; ok {
		params.AuthTokenEncrypted = tokenEnc
	}

	if headerValueEnc, ok := encryptedAuth["header_value"]; ok {
		params.AuthHeaderValueEncrypted = headerValueEnc
	}

	// Create connection in database
	conn, err := cs.queries.CreateConnection(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}

	// Return connection info with decrypted auth
	return &ConnectionInfo{
		ID:          conn.ID,
		Name:        conn.Name,
		Description: conn.Description.String,
		BaseURL:     conn.BaseUrl,
		Auth:        req.Auth,
		CreatedAt:   conn.CreatedAt,
		UpdatedAt:   conn.UpdatedAt,
	}, nil
}

// GetConnection retrieves a connection by name with decrypted auth details
func (cs *ConnectionService) GetConnection(ctx context.Context, name string) (*ConnectionInfo, error) {
	conn, err := cs.queries.GetConnectionByName(ctx, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection: %w", err)
	}

	// Decrypt auth details
	auth, err := cs.encKey.DecryptConnectionAuth(
		conn.AuthType,
		conn.AuthUsername.String,
		conn.AuthHeaderName.String,
		conn.AuthPasswordEncrypted,
		conn.AuthTokenEncrypted,
		conn.AuthHeaderValueEncrypted,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt auth data: %w", err)
	}

	return &ConnectionInfo{
		ID:          conn.ID,
		Name:        conn.Name,
		Description: conn.Description.String,
		BaseURL:     conn.BaseUrl,
		Auth:        *auth,
		CreatedAt:   conn.CreatedAt,
		UpdatedAt:   conn.UpdatedAt,
	}, nil
}

// GetConnectionByID retrieves a connection by ID with decrypted auth details
func (cs *ConnectionService) GetConnectionByID(ctx context.Context, id int64) (*ConnectionInfo, error) {
	conn, err := cs.queries.GetConnectionByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection: %w", err)
	}

	// Decrypt auth details
	auth, err := cs.encKey.DecryptConnectionAuth(
		conn.AuthType,
		conn.AuthUsername.String,
		conn.AuthHeaderName.String,
		conn.AuthPasswordEncrypted,
		conn.AuthTokenEncrypted,
		conn.AuthHeaderValueEncrypted,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt auth data: %w", err)
	}

	return &ConnectionInfo{
		ID:          conn.ID,
		Name:        conn.Name,
		Description: conn.Description.String,
		BaseURL:     conn.BaseUrl,
		Auth:        *auth,
		CreatedAt:   conn.CreatedAt,
		UpdatedAt:   conn.UpdatedAt,
	}, nil
}

// ListConnections retrieves all connections (without decrypted auth details for security)
func (cs *ConnectionService) ListConnections(ctx context.Context) ([]ConnectionInfo, error) {
	connections, err := cs.queries.ListConnections(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list connections: %w", err)
	}

	result := make([]ConnectionInfo, len(connections))
	for i, conn := range connections {
		result[i] = ConnectionInfo{
			ID:          conn.ID,
			Name:        conn.Name,
			Description: conn.Description.String,
			BaseURL:     conn.BaseUrl,
			Auth: util.ConnectionAuth{
				Type:       conn.AuthType,
				Username:   conn.AuthUsername.String,
				HeaderName: conn.AuthHeaderName.String,
				// Don't include sensitive data in list view
			},
			CreatedAt: conn.CreatedAt,
			UpdatedAt: conn.UpdatedAt,
		}
	}

	return result, nil
}

// DeleteConnection deletes a connection by name
func (cs *ConnectionService) DeleteConnection(ctx context.Context, name string) error {
	err := cs.queries.DeleteConnectionByName(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to delete connection: %w", err)
	}
	return nil
}

// isValidAuthType checks if the auth type is valid
func isValidAuthType(authType string) bool {
	validTypes := []string{"none", "basic", "bearer", "custom_header"}
	for _, valid := range validTypes {
		if authType == valid {
			return true
		}
	}
	return false
}

// SchemaService provides high-level operations for schema management
type SchemaService struct {
	queries *db.Queries
}

// NewSchemaService creates a new schema service
func NewSchemaService(queries *db.Queries) *SchemaService {
	return &SchemaService{
		queries: queries,
	}
}

// CreateSchemaRequest represents a request to create a new schema
type CreateSchemaRequest struct {
	Name        string
	Description string
	Filename    string
	Version     string
}

// CreateSchema creates a new OpenAPI schema record
func (ss *SchemaService) CreateSchema(ctx context.Context, req CreateSchemaRequest) (*db.OpenapiSchema, error) {
	params := db.CreateOpenAPISchemaParams{
		Name:     req.Name,
		Filename: req.Filename,
	}

	if req.Description != "" {
		params.Description = pgtype.Text{String: req.Description, Valid: true}
	}

	if req.Version != "" {
		params.Version = pgtype.Text{String: req.Version, Valid: true}
	}

	schema, err := ss.queries.CreateOpenAPISchema(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create schema: %w", err)
	}

	return &schema, nil
}

// AssociateConnection associates a connection with a schema
func (ss *SchemaService) AssociateConnection(ctx context.Context, schemaName, connectionName string, isDefault bool) error {
	// Get schema by name
	schema, err := ss.queries.GetOpenAPISchemaByName(ctx, schemaName)
	if err != nil {
		return fmt.Errorf("failed to get schema: %w", err)
	}

	// Get connection by name
	conn, err := ss.queries.GetConnectionByName(ctx, connectionName)
	if err != nil {
		return fmt.Errorf("failed to get connection: %w", err)
	}

	// If setting as default, clear other defaults first
	if isDefault {
		err = ss.queries.SetDefaultConnection(ctx, db.SetDefaultConnectionParams{
			SchemaID:     schema.ID,
			ConnectionID: conn.ID,
		})
		if err != nil {
			return fmt.Errorf("failed to set default connection: %w", err)
		}
	} else {
		// Create association
		_, err = ss.queries.CreateSchemaConnection(ctx, db.CreateSchemaConnectionParams{
			SchemaID:     schema.ID,
			ConnectionID: conn.ID,
			IsDefault:    isDefault,
		})
		if err != nil {
			return fmt.Errorf("failed to associate connection: %w", err)
		}
	}

	return nil
}

// DisassociateConnection removes a connection association from a schema
func (ss *SchemaService) DisassociateConnection(ctx context.Context, schemaName, connectionName string) error {
	// Get schema by name
	schema, err := ss.queries.GetOpenAPISchemaByName(ctx, schemaName)
	if err != nil {
		return fmt.Errorf("failed to get schema: %w", err)
	}

	// Get connection by name
	conn, err := ss.queries.GetConnectionByName(ctx, connectionName)
	if err != nil {
		return fmt.Errorf("failed to get connection: %w", err)
	}

	// Remove association
	err = ss.queries.DeleteSchemaConnection(ctx, db.DeleteSchemaConnectionParams{
		SchemaID:     schema.ID,
		ConnectionID: conn.ID,
	})
	if err != nil {
		return fmt.Errorf("failed to disassociate connection: %w", err)
	}

	return nil
}
