package tui

import (
	"fmt"
	"log"

	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/db"
)

type Model struct {
	dbConnectionString string
	currentView        viewState
	mainMenu           mainMenuModel
	profileModel       profileModel
	toolModel          toolModel
	userModel          userModel
	roleModel          roleModel
	queries            db.Querier
}

type viewState int

const (
	mainMenuView viewState = iota
	profileView
	toolView
	userView
	roleView
)

func InitialModel(dbConnectionString string) tea.Model {
	queries, err := connectDB(dbConnectionString)
	if err != nil {
		log.Fatalf("Failed to connect to database for TUI: %v", err)
	}
	return Model{
		dbConnectionString: dbConnectionString,
		currentView:        mainMenuView,
		mainMenu:           newMainMenuModel(),
		profileModel:       newProfileModel(queries),
		toolModel:          newToolModel(queries),
		userModel:          newUserModel(queries),
		roleModel:          newRoleModel(queries),
		queries:            queries,
	}
}

// InitialModelWithQueries creates a TUI model with injected database queries (useful for testing)
func InitialModelWithQueries(queries db.Querier) tea.Model {
	return Model{
		dbConnectionString: "test",
		currentView:        mainMenuView,
		mainMenu:           newMainMenuModel(),
		profileModel:       newProfileModel(queries),
		toolModel:          newToolModel(queries),
		userModel:          newUserModel(queries),
		roleModel:          newRoleModel(queries),
		queries:            queries,
	}
}

func (m Model) Init() tea.Cmd {
	return m.mainMenu.Init()
}

func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "esc":
			if m.currentView != mainMenuView {
				m.currentView = mainMenuView
				return m, nil
			}
		}

	case selectMenuItemMsg:
		switch msg {
		case profileManagementSelected:
			m.currentView = profileView
			cmds = append(cmds, m.profileModel.Init())
		case toolAssociationSelected:
			m.currentView = toolView
			cmds = append(cmds, m.toolModel.Init())
		case userManagementSelected:
			m.currentView = userView
			cmds = append(cmds, m.userModel.Init())
		case roleManagementSelected:
			m.currentView = roleView
			cmds = append(cmds, m.roleModel.Init())
		case exitSelected:
			return m, tea.Quit
		}
	}

	// Update the appropriate sub-model
	switch m.currentView {
	case mainMenuView:
		var updatedModel tea.Model
		updatedModel, cmd = m.mainMenu.Update(msg)
		m.mainMenu = updatedModel.(mainMenuModel)
		cmds = append(cmds, cmd)
	case profileView:
		var updatedModel tea.Model
		updatedModel, cmd = m.profileModel.Update(msg)
		m.profileModel = updatedModel.(profileModel)
		cmds = append(cmds, cmd)
	case toolView:
		var updatedModel tea.Model
		updatedModel, cmd = m.toolModel.Update(msg)
		m.toolModel = updatedModel.(toolModel)
		cmds = append(cmds, cmd)
	case userView:
		var updatedModel tea.Model
		updatedModel, cmd = m.userModel.Update(msg)
		m.userModel = updatedModel.(userModel)
		cmds = append(cmds, cmd)
	case roleView:
		var updatedModel tea.Model
		updatedModel, cmd = m.roleModel.Update(msg)
		m.roleModel = updatedModel.(roleModel)
		cmds = append(cmds, cmd)
	}

	return m, tea.Batch(cmds...)
}

func (m Model) View() string {
	switch m.currentView {
	case mainMenuView:
		return m.mainMenu.View()
	case profileView:
		return m.profileModel.View()
	case toolView:
		return m.toolModel.View()
	case userView:
		return m.userModel.View()
	case roleView:
		return m.roleModel.View()
	default:
		return "Unknown view."
	}
}

func connectDB(dbConnectionString string) (*db.Queries, error) {
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}
	// defer conn.Close(context.Background()) // Do not close here, connection needs to persist for TUI session
	return db.New(conn), nil
}

type mainMenuModel struct {
	choices []string
	cursor  int
}

func newMainMenuModel() mainMenuModel {
	return mainMenuModel{
		choices: []string{"Profile Management", "Tool Association", "User Management", "Role Management", "Exit"},
	}
}

func (mm mainMenuModel) Init() tea.Cmd {
	return nil
}

func (mm mainMenuModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "up", "k":
			if mm.cursor > 0 {
				mm.cursor--
			}
		case "down", "j":
			if mm.cursor < len(mm.choices)-1 {
				mm.cursor++
			}
		case "enter":
			return mm, func() tea.Msg {
				return selectMenuItemMsg(mm.cursor)
			}
		}
	}
	return mm, nil
}

func (mm mainMenuModel) View() string {
	s := "Welcome to SUSE AIR TUI!\n\n"
	s += "What would you like to manage?\n\n"

	for i, choice := range mm.choices {
		cursor := " "
		if mm.cursor == i {
			cursor = ">"
		}
		s += fmt.Sprintf("%s %s\n", cursor, choice)
	}

	s += "\nPress q to quit.\n"
	return s
}

type selectMenuItemMsg int

const (
	profileManagementSelected selectMenuItemMsg = iota
	toolAssociationSelected
	userManagementSelected
	roleManagementSelected
	exitSelected
)
