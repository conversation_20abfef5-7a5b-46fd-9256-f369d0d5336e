package tui

import (
	"context"
	"fmt"

	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/db"
)

type roleModel struct {
	queries  db.Querier
	roles    []db.Role
	profiles []db.Profile
	cursor   int
	selected int // index of selected role for details/update/delete
	state    roleState

	// Input fields for create/update
	nameInput    textinput.Model
	newNameInput textinput.Model

	// For profile association
	profileCursor int
	selectedRole  db.Role

	// Status message
	statusMessage string
}

type roleState int

const (
	roleList roleState = iota
	roleCreateForm
	roleDetail
	roleUpdateForm
	roleDeleteConfirm
	roleProfileAssociation
	roleProfileList
)

func newRoleModel(queries db.Querier) roleModel {
	nameInput := textinput.New()
	nameInput.Placeholder = "Role name"
	nameInput.CharLimit = 50

	newNameInput := textinput.New()
	newNameInput.Placeholder = "New role name"
	newNameInput.CharLimit = 50

	return roleModel{
		queries:      queries,
		roles:        []db.Role{},
		profiles:     []db.Profile{},
		cursor:       0,
		selected:     0,
		state:        roleList,
		nameInput:    nameInput,
		newNameInput: newNameInput,
	}
}

func (rm roleModel) Init() tea.Cmd {
	return rm.fetchRolesCmd()
}

func (rm roleModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return rm, tea.Quit

		case "esc":
			if rm.state != roleList {
				rm.state = roleList
				rm.statusMessage = ""
				return rm, rm.fetchRolesCmd()
			}

		case "up", "k":
			if rm.state == roleList && rm.cursor > 0 {
				rm.cursor--
			} else if rm.state == roleProfileAssociation && rm.profileCursor > 0 {
				rm.profileCursor--
			}

		case "down", "j":
			if rm.state == roleList && rm.cursor < len(rm.roles)-1 {
				rm.cursor++
			} else if rm.state == roleProfileAssociation && rm.profileCursor < len(rm.profiles)-1 {
				rm.profileCursor++
			}

		case "c": // Create new role
			if rm.state == roleList {
				rm.state = roleCreateForm
				rm.nameInput.Focus()
				rm.statusMessage = ""
				return rm, textinput.Blink
			}

		case "enter":
			switch rm.state {
			case roleList:
				if len(rm.roles) > 0 {
					rm.selected = rm.cursor
					rm.state = roleDetail
					rm.statusMessage = ""
				}

			case roleCreateForm:
				if rm.nameInput.Value() != "" {
					return rm, rm.createRoleCmd(rm.nameInput.Value())
				}

			case roleUpdateForm:
				if rm.newNameInput.Value() != "" {
					return rm, rm.updateRoleCmd(rm.roles[rm.selected].ID, rm.newNameInput.Value())
				}

			case roleProfileAssociation:
				if len(rm.profiles) > 0 {
					profile := rm.profiles[rm.profileCursor]
					return rm, rm.grantProfileAccessCmd(rm.selectedRole.ID, profile.ID)
				}
			}

		case "u": // Update role
			if rm.state == roleDetail && len(rm.roles) > 0 {
				rm.state = roleUpdateForm
				rm.newNameInput.SetValue(rm.roles[rm.selected].Name)
				rm.newNameInput.Focus()
				rm.statusMessage = ""
				return rm, textinput.Blink
			}

		case "d": // Delete role
			if rm.state == roleDetail && len(rm.roles) > 0 {
				rm.state = roleDeleteConfirm
				rm.statusMessage = ""
			}

		case "p": // Manage profile access
			if rm.state == roleDetail && len(rm.roles) > 0 {
				rm.selectedRole = rm.roles[rm.selected]
				rm.state = roleProfileAssociation
				rm.profileCursor = 0
				rm.statusMessage = ""
				return rm, rm.fetchProfilesCmd()
			}

		case "l": // List profiles accessible by role
			if rm.state == roleDetail && len(rm.roles) > 0 {
				rm.selectedRole = rm.roles[rm.selected]
				rm.state = roleProfileList
				rm.statusMessage = ""
				return rm, rm.fetchRoleProfilesCmd(rm.selectedRole.ID)
			}

		case "y", "Y": // Confirm delete
			if rm.state == roleDeleteConfirm && len(rm.roles) > 0 {
				return rm, rm.deleteRoleCmd(rm.roles[rm.selected].ID)
			}

		case "n", "N": // Cancel delete
			if rm.state == roleDeleteConfirm {
				rm.state = roleDetail
				rm.statusMessage = "Delete cancelled."
			}

		case "tab":
			if rm.state == roleCreateForm {
				// No additional fields to tab to in role creation
			} else if rm.state == roleUpdateForm {
				// No additional fields to tab to in role update
			}
		}

	case rolesFetchedMsg: // Roles loaded
		rm.roles = msg
		rm.cursor = 0
		rm.statusMessage = ""
		return rm, nil

	case roleCreatedMsg: // Role created
		rm.state = roleList
		rm.nameInput.SetValue("")
		rm.statusMessage = fmt.Sprintf("Role '%s' created successfully!", msg.Name)
		return rm, rm.fetchRolesCmd()

	case roleUpdatedMsg: // Role updated
		rm.state = roleDetail
		rm.newNameInput.SetValue("")
		rm.statusMessage = fmt.Sprintf("Role updated to '%s' successfully!", msg.Name)
		return rm, rm.fetchRolesCmd()

	case roleDeletedMsg: // Role deleted
		rm.state = roleList
		rm.statusMessage = fmt.Sprintf("Role deleted successfully!")
		return rm, rm.fetchRolesCmd()

	case roleAssociationProfilesFetchedMsg: // Profiles loaded for association
		rm.profiles = msg
		rm.profileCursor = 0
		return rm, nil

	case profileAccessGrantedMsg: // Profile access granted
		rm.state = roleDetail
		rm.statusMessage = fmt.Sprintf("Profile access granted successfully!")
		return rm, nil

	case roleProfilesFetchedMsg: // Role profiles loaded
		rm.profiles = msg
		return rm, nil

	case errMsg:
		rm.statusMessage = fmt.Sprintf("Error: %s", msg.Error())
		return rm, nil
	}

	// Handle text input updates
	switch rm.state {
	case roleCreateForm:
		rm.nameInput, cmd = rm.nameInput.Update(msg)
	case roleUpdateForm:
		rm.newNameInput, cmd = rm.newNameInput.Update(msg)
	}

	return rm, cmd
}

func (rm roleModel) View() string {
	s := ""

	s += "\nRole Management\n"
	s += "---------------\n"

	switch rm.state {
	case roleList:
		s += "Roles:\n"
		if len(rm.roles) == 0 {
			s += "  No roles found. Press 'c' to create one.\n"
		} else {
			for i, r := range rm.roles {
				cursor := " "
				if rm.cursor == i {
					cursor = ">"
				}
				s += fmt.Sprintf("%s %s\n", cursor, r.Name)
			}
			s += "\nPress 'enter' to view details, 'c' to create, 'q' to quit.\n"
		}

	case roleCreateForm:
		s += "Create New Role:\n"
		s += fmt.Sprintf("  %s\n", rm.nameInput.View())
		s += "\nPress Enter to submit, Esc to cancel.\n"

	case roleDetail:
		if rm.selected < len(rm.roles) {
			role := rm.roles[rm.selected]
			s += fmt.Sprintf("Role Details:\n")
			s += fmt.Sprintf("  ID: %d\n", role.ID)
			s += fmt.Sprintf("  Name: %s\n", role.Name)
			s += fmt.Sprintf("  Created: %s\n", role.CreatedAt.Time.Format("2006-01-02 15:04:05"))
			s += fmt.Sprintf("  Updated: %s\n", role.UpdatedAt.Time.Format("2006-01-02 15:04:05"))
			s += "\nPress 'u' to update, 'd' to delete, 'p' to manage profile access, 'l' to list accessible profiles, 'esc' to go back.\n"
		}

	case roleUpdateForm:
		s += "Update Role:\n"
		s += fmt.Sprintf("  %s\n", rm.newNameInput.View())
		s += "\nPress Enter to submit, Esc to cancel.\n"

	case roleDeleteConfirm:
		if rm.selected < len(rm.roles) {
			role := rm.roles[rm.selected]
			s += fmt.Sprintf("Are you sure you want to delete role '%s'? (y/N)\n", role.Name)
		}

	case roleProfileAssociation:
		s += fmt.Sprintf("Grant Profile Access to Role '%s':\n", rm.selectedRole.Name)
		if len(rm.profiles) == 0 {
			s += "  No profiles found.\n"
		} else {
			for i, p := range rm.profiles {
				cursor := " "
				if rm.profileCursor == i {
					cursor = ">"
				}
				s += fmt.Sprintf("%s %s (%s)\n", cursor, p.Name, p.PathSegment)
			}
			s += "\nPress 'enter' to grant access, 'esc' to go back.\n"
		}

	case roleProfileList:
		s += fmt.Sprintf("Profiles Accessible by Role '%s':\n", rm.selectedRole.Name)
		if len(rm.profiles) == 0 {
			s += "  No profiles accessible.\n"
		} else {
			for _, p := range rm.profiles {
				s += fmt.Sprintf("  %s (%s) - %s\n", p.Name, p.PathSegment, p.Description.String)
			}
		}
		s += "\nPress 'esc' to go back.\n"
	}

	if rm.statusMessage != "" {
		s += fmt.Sprintf("\n%s\n", rm.statusMessage)
	}

	return s
}

// Commands

type rolesFetchedMsg []db.Role
type roleCreatedMsg db.Role
type roleUpdatedMsg db.Role
type roleDeletedMsg string
type roleProfilesFetchedMsg []db.Profile
type profileAccessGrantedMsg string
type roleAssociationProfilesFetchedMsg []db.Profile

func (rm roleModel) fetchRolesCmd() tea.Cmd {
	return func() tea.Msg {
		roles, err := rm.queries.ListRoles(context.Background())
		if err != nil {
			return errMsg(fmt.Errorf("could not fetch roles: %w", err))
		}
		return rolesFetchedMsg(roles)
	}
}

func (rm roleModel) createRoleCmd(name string) tea.Cmd {
	return func() tea.Msg {
		role, err := rm.queries.CreateRole(context.Background(), name)
		if err != nil {
			return errMsg(fmt.Errorf("could not create role: %w", err))
		}
		return roleCreatedMsg(role)
	}
}

func (rm roleModel) updateRoleCmd(id int64, name string) tea.Cmd {
	return func() tea.Msg {
		role, err := rm.queries.UpdateRole(context.Background(), db.UpdateRoleParams{
			ID:   id,
			Name: name,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not update role: %w", err))
		}
		return roleUpdatedMsg(role)
	}
}

func (rm roleModel) deleteRoleCmd(id int64) tea.Cmd {
	return func() tea.Msg {
		err := rm.queries.DeleteRole(context.Background(), id)
		if err != nil {
			return errMsg(fmt.Errorf("could not delete role: %w", err))
		}
		return roleDeletedMsg("deleted")
	}
}

func (rm roleModel) fetchProfilesCmd() tea.Cmd {
	return func() tea.Msg {
		profiles, err := rm.queries.ListProfiles(context.Background())
		if err != nil {
			return errMsg(fmt.Errorf("could not fetch profiles: %w", err))
		}
		return roleAssociationProfilesFetchedMsg(profiles)
	}
}

func (rm roleModel) grantProfileAccessCmd(roleID, profileID int64) tea.Cmd {
	return func() tea.Msg {
		_, err := rm.queries.CreateRoleProfile(context.Background(), db.CreateRoleProfileParams{
			RoleID:    roleID,
			ProfileID: profileID,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not grant profile access: %w", err))
		}
		return profileAccessGrantedMsg("granted")
	}
}

func (rm roleModel) fetchRoleProfilesCmd(roleID int64) tea.Cmd {
	return func() tea.Msg {
		profiles, err := rm.queries.ListProfilesByRole(context.Background(), roleID)
		if err != nil {
			return errMsg(fmt.Errorf("could not fetch role profiles: %w", err))
		}
		return roleProfilesFetchedMsg(profiles)
	}
}
