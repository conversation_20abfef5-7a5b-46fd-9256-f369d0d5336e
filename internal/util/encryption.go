package util

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"os"
)

// Encryption<PERSON>ey represents the encryption key used for sensitive data
type Encryption<PERSON>ey []byte

// GetEncryption<PERSON>ey retrieves the encryption key from environment variable
// If not set, generates a warning and uses a default key (not for production)
func GetEncryptionKey() EncryptionKey {
	keyStr := os.Getenv("SAIR_ENCRYPTION_KEY")
	if keyStr == "" {
		// For development/testing, use a default key
		// In production, this should be a proper error
		fmt.Println("WARNING: SAIR_ENCRYPTION_KEY not set, using default key (not secure for production)")
		keyStr = "default-key-for-development-only-not-secure"
	}
	
	// Create a 32-byte key from the string using SHA256
	hash := sha256.Sum256([]byte(keyStr))
	return EncryptionKey(hash[:])
}

// Encrypt encrypts plaintext using AES-GCM
func (key EncryptionKey) Encrypt(plaintext string) ([]byte, error) {
	if plaintext == "" {
		return nil, nil
	}
	
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}
	
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}
	
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return ciphertext, nil
}

// Decrypt decrypts ciphertext using AES-GCM
func (key EncryptionKey) Decrypt(ciphertext []byte) (string, error) {
	if len(ciphertext) == 0 {
		return "", nil
	}
	
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}
	
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}
	
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}
	
	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %w", err)
	}
	
	return string(plaintext), nil
}

// EncryptToBase64 encrypts plaintext and returns base64 encoded result
func (key EncryptionKey) EncryptToBase64(plaintext string) (string, error) {
	encrypted, err := key.Encrypt(plaintext)
	if err != nil {
		return "", err
	}
	if encrypted == nil {
		return "", nil
	}
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// DecryptFromBase64 decrypts base64 encoded ciphertext
func (key EncryptionKey) DecryptFromBase64(encoded string) (string, error) {
	if encoded == "" {
		return "", nil
	}
	
	ciphertext, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}
	
	return key.Decrypt(ciphertext)
}

// ConnectionAuth represents authentication details for a connection
type ConnectionAuth struct {
	Type        string
	Username    string
	Password    string
	Token       string
	HeaderName  string
	HeaderValue string
}

// EncryptConnectionAuth encrypts the sensitive fields of connection auth
func (key EncryptionKey) EncryptConnectionAuth(auth *ConnectionAuth) (map[string][]byte, error) {
	result := make(map[string][]byte)
	
	if auth.Password != "" {
		encrypted, err := key.Encrypt(auth.Password)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt password: %w", err)
		}
		result["password"] = encrypted
	}
	
	if auth.Token != "" {
		encrypted, err := key.Encrypt(auth.Token)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt token: %w", err)
		}
		result["token"] = encrypted
	}
	
	if auth.HeaderValue != "" {
		encrypted, err := key.Encrypt(auth.HeaderValue)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt header value: %w", err)
		}
		result["header_value"] = encrypted
	}
	
	return result, nil
}

// DecryptConnectionAuth decrypts the sensitive fields of connection auth
func (key EncryptionKey) DecryptConnectionAuth(authType, username, headerName string, 
	passwordEncrypted, tokenEncrypted, headerValueEncrypted []byte) (*ConnectionAuth, error) {
	
	auth := &ConnectionAuth{
		Type:       authType,
		Username:   username,
		HeaderName: headerName,
	}
	
	if len(passwordEncrypted) > 0 {
		password, err := key.Decrypt(passwordEncrypted)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt password: %w", err)
		}
		auth.Password = password
	}
	
	if len(tokenEncrypted) > 0 {
		token, err := key.Decrypt(tokenEncrypted)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt token: %w", err)
		}
		auth.Token = token
	}
	
	if len(headerValueEncrypted) > 0 {
		headerValue, err := key.Decrypt(headerValueEncrypted)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt header value: %w", err)
		}
		auth.HeaderValue = headerValue
	}
	
	return auth, nil
}
