// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"github.com/jackc/pgx/v5/pgtype"
)

// Stores connection details for backend REST services
type Connection struct {
	ID int64 `json:"id"`
	// Unique name for the connection
	Name string `json:"name"`
	// Optional description of the connection
	Description pgtype.Text `json:"description"`
	// Base URL of the backend service (e.g., https://api.example.com)
	BaseUrl string `json:"base_url"`
	// Type of authentication: none, basic, bearer, or custom_header
	AuthType string `json:"auth_type"`
	// Username for basic authentication
	AuthUsername pgtype.Text `json:"auth_username"`
	// Encrypted password for basic authentication
	AuthPasswordEncrypted []byte `json:"auth_password_encrypted"`
	// Encrypted token for bearer authentication
	AuthTokenEncrypted []byte `json:"auth_token_encrypted"`
	// Header name for custom header authentication
	AuthHeaderName pgtype.Text `json:"auth_header_name"`
	// Encrypted header value for custom header authentication
	AuthHeaderValueEncrypted []byte             `json:"auth_header_value_encrypted"`
	CreatedAt                pgtype.Timestamptz `json:"created_at"`
	UpdatedAt                pgtype.Timestamptz `json:"updated_at"`
}

type McpTool struct {
	ID int64 `json:"id"`
	// The unique name for the MCP tool, e.g., createUser.
	ToolName string `json:"tool_name"`
	// The description of what the tool does, taken from the OpenAPI summary.
	Description pgtype.Text `json:"description"`
	// The JSON schema for the tool's input, combining all parameters.
	InputSchema []byte             `json:"input_schema"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
}

type McpToolMapping struct {
	ID int64 `json:"id"`
	// Foreign key to the mcp_tools table.
	McpToolID int64 `json:"mcp_tool_id"`
	// The original OpenAPI path template, e.g., /users/{userId}.
	OpenapiPath string `json:"openapi_path"`
	// The HTTP method for the endpoint (e.g., POST, GET).
	HttpMethod string `json:"http_method"`
	// A JSON object mapping MCP properties back to their OpenAPI parameter sources (path, query, header).
	ParamMappings []byte `json:"param_mappings"`
	// A JSON object describing how to reconstruct the original request body.
	BodyMapping []byte             `json:"body_mapping"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	// Foreign key to the openapi_operations table
	OperationID pgtype.Int8 `json:"operation_id"`
}

// Stores individual operations from OpenAPI schemas
type OpenapiOperation struct {
	ID int64 `json:"id"`
	// Foreign key to the openapi_schemas table
	SchemaID int64 `json:"schema_id"`
	// The operationId from the OpenAPI specification
	OperationID string `json:"operation_id"`
	// The path template from the OpenAPI specification
	Path string `json:"path"`
	// The HTTP method (GET, POST, etc.)
	Method string `json:"method"`
	// Summary from the OpenAPI operation
	Summary pgtype.Text `json:"summary"`
	// Description from the OpenAPI operation
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
}

// Stores references to OpenAPI schema files
type OpenapiSchema struct {
	ID int64 `json:"id"`
	// Unique name for the schema
	Name string `json:"name"`
	// Optional description of the schema
	Description pgtype.Text `json:"description"`
	// Original filename or identifier of the OpenAPI spec
	Filename string `json:"filename"`
	// Version of the OpenAPI specification
	Version   pgtype.Text        `json:"version"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
}

// Profiles are groups of MCP tools.
type Profile struct {
	ID int64 `json:"id"`
	// The unique name for the profile, e.g., "billing_admin_tools".
	Name string `json:"name"`
	// A description of what this profile contains.
	Description pgtype.Text `json:"description"`
	// A unique string for the URL path, e.g., "billing".
	PathSegment string             `json:"path_segment"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
}

// Maps tools to profiles with an Access Control List (ACL).
type ProfileTool struct {
	ProfileID int64 `json:"profile_id"`
	ToolID    int64 `json:"tool_id"`
	// Access control level for the tool in this profile.
	Acl string `json:"acl"`
}

// Roles define user roles within the system.
type Role struct {
	ID int64 `json:"id"`
	// The unique name for the role, e.g., "admin", "viewer".
	Name      string             `json:"name"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
}

// Maps which roles have access to which profiles.
type RoleProfile struct {
	RoleID    int64 `json:"role_id"`
	ProfileID int64 `json:"profile_id"`
}

// Many-to-many relationship between schemas and connections
type SchemaConnection struct {
	// Foreign key to the openapi_schemas table
	SchemaID int64 `json:"schema_id"`
	// Foreign key to the connections table
	ConnectionID int64 `json:"connection_id"`
	// Whether this is the default connection for the schema
	IsDefault bool               `json:"is_default"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
}

type User struct {
	ID int64 `json:"id"`
	// The unique username for the user.
	Username string `json:"username"`
	// Hashed password for the user.
	PasswordHash string             `json:"password_hash"`
	CreatedAt    pgtype.Timestamptz `json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `json:"updated_at"`
}

// Maps users to roles.
type UserRole struct {
	UserID int64 `json:"user_id"`
	RoleID int64 `json:"role_id"`
}
