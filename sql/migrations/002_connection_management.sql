-- SUSE AIR: Connection Management & Execution Context
-- Phase 5: Add tables for managing connections to backend REST services

-- +goose Up

-- Connections table to store backend service connection details
CREATE TABLE connections (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    base_url TEXT NOT NULL,
    auth_type TEXT NOT NULL CHECK (auth_type IN ('none', 'basic', 'bearer', 'custom_header')),
    auth_username TEXT, -- For basic auth
    auth_password_encrypted BYTEA, -- Encrypted password for basic auth
    auth_token_encrypted BYTEA, -- Encrypted token for bearer auth
    auth_header_name TEXT, -- For custom header auth
    auth_header_value_encrypted BYTEA, -- Encrypted header value for custom header auth
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_connections_name ON connections (name);
COMMENT ON TABLE connections IS 'Stores connection details for backend REST services';
COMMENT ON COLUMN connections.name IS 'Unique name for the connection';
COMMENT ON COLUMN connections.description IS 'Optional description of the connection';
COMMENT ON COLUMN connections.base_url IS 'Base URL of the backend service (e.g., https://api.example.com)';
COMMENT ON COLUMN connections.auth_type IS 'Type of authentication: none, basic, bearer, or custom_header';
COMMENT ON COLUMN connections.auth_username IS 'Username for basic authentication';
COMMENT ON COLUMN connections.auth_password_encrypted IS 'Encrypted password for basic authentication';
COMMENT ON COLUMN connections.auth_token_encrypted IS 'Encrypted token for bearer authentication';
COMMENT ON COLUMN connections.auth_header_name IS 'Header name for custom header authentication';
COMMENT ON COLUMN connections.auth_header_value_encrypted IS 'Encrypted header value for custom header authentication';

-- OpenAPI schemas table to store references to original schema files
CREATE TABLE openapi_schemas (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    filename TEXT NOT NULL,
    version TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_openapi_schemas_name ON openapi_schemas (name);
CREATE INDEX idx_openapi_schemas_filename ON openapi_schemas (filename);
COMMENT ON TABLE openapi_schemas IS 'Stores references to OpenAPI schema files';
COMMENT ON COLUMN openapi_schemas.name IS 'Unique name for the schema';
COMMENT ON COLUMN openapi_schemas.description IS 'Optional description of the schema';
COMMENT ON COLUMN openapi_schemas.filename IS 'Original filename or identifier of the OpenAPI spec';
COMMENT ON COLUMN openapi_schemas.version IS 'Version of the OpenAPI specification';

-- OpenAPI operations table to link operations to schemas
CREATE TABLE openapi_operations (
    id BIGSERIAL PRIMARY KEY,
    schema_id BIGINT NOT NULL REFERENCES openapi_schemas(id) ON DELETE CASCADE,
    operation_id TEXT NOT NULL,
    path TEXT NOT NULL,
    method TEXT NOT NULL,
    summary TEXT,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_openapi_operations_schema_operation ON openapi_operations (schema_id, operation_id);
CREATE INDEX idx_openapi_operations_schema_id ON openapi_operations (schema_id);
COMMENT ON TABLE openapi_operations IS 'Stores individual operations from OpenAPI schemas';
COMMENT ON COLUMN openapi_operations.schema_id IS 'Foreign key to the openapi_schemas table';
COMMENT ON COLUMN openapi_operations.operation_id IS 'The operationId from the OpenAPI specification';
COMMENT ON COLUMN openapi_operations.path IS 'The path template from the OpenAPI specification';
COMMENT ON COLUMN openapi_operations.method IS 'The HTTP method (GET, POST, etc.)';
COMMENT ON COLUMN openapi_operations.summary IS 'Summary from the OpenAPI operation';
COMMENT ON COLUMN openapi_operations.description IS 'Description from the OpenAPI operation';

-- Schema connections join table for many-to-many relationship
CREATE TABLE schema_connections (
    schema_id BIGINT NOT NULL REFERENCES openapi_schemas(id) ON DELETE CASCADE,
    connection_id BIGINT NOT NULL REFERENCES connections(id) ON DELETE CASCADE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (schema_id, connection_id)
);

CREATE INDEX idx_schema_connections_schema_id ON schema_connections (schema_id);
CREATE INDEX idx_schema_connections_connection_id ON schema_connections (connection_id);
CREATE INDEX idx_schema_connections_default ON schema_connections (schema_id, is_default) WHERE is_default = TRUE;
COMMENT ON TABLE schema_connections IS 'Many-to-many relationship between schemas and connections';
COMMENT ON COLUMN schema_connections.schema_id IS 'Foreign key to the openapi_schemas table';
COMMENT ON COLUMN schema_connections.connection_id IS 'Foreign key to the connections table';
COMMENT ON COLUMN schema_connections.is_default IS 'Whether this is the default connection for the schema';

-- Add foreign key to mcp_tool_mappings to link to openapi_operations
ALTER TABLE mcp_tool_mappings 
ADD COLUMN operation_id BIGINT REFERENCES openapi_operations(id) ON DELETE SET NULL;

CREATE INDEX idx_mcp_tool_mappings_operation_id ON mcp_tool_mappings (operation_id);
COMMENT ON COLUMN mcp_tool_mappings.operation_id IS 'Foreign key to the openapi_operations table';

-- +goose Down

-- Remove the foreign key from mcp_tool_mappings
ALTER TABLE mcp_tool_mappings DROP COLUMN IF EXISTS operation_id;

-- Drop tables in reverse order to handle foreign key constraints
DROP TABLE IF EXISTS schema_connections CASCADE;
DROP TABLE IF EXISTS openapi_operations CASCADE;
DROP TABLE IF EXISTS openapi_schemas CASCADE;
DROP TABLE IF EXISTS connections CASCADE;
