-- SUSE AIR: Consolidated Database Schema
-- This is a single, valid Goose migration file.

-- +goose Up
-- Create all tables in an order that respects foreign key constraints.

-- Core tool definition tables
CREATE TABLE mcp_tools (
                           id BIGSERIAL PRIMARY KEY,
                           tool_name TEXT NOT NULL,
                           description TEXT,
                           input_schema JSONB NOT NULL,
                           created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                           updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_tool_name ON mcp_tools (tool_name);
COMMENT ON COLUMN mcp_tools.tool_name IS 'The unique name for the MCP tool, e.g., createUser.';
COMMENT ON COLUMN mcp_tools.description IS 'The description of what the tool does, taken from the OpenAPI summary.';
COMMENT ON COLUMN mcp_tools.input_schema IS 'The JSON schema for the tool''s input, combining all parameters.';

CREATE TABLE mcp_tool_mappings (
                                   id BIGSERIAL PRIMARY KEY,
                                   mcp_tool_id BIGINT NOT NULL REFERENCES mcp_tools(id) ON DELETE CASCADE,
                                   openapi_path TEXT NOT NULL,
                                   http_method TEXT NOT NULL,
                                   param_mappings JSONB,
                                   body_mapping JSONB,
                                   created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_mcp_tool_id ON mcp_tool_mappings (mcp_tool_id);
COMMENT ON COLUMN mcp_tool_mappings.mcp_tool_id IS 'Foreign key to the mcp_tools table.';
COMMENT ON COLUMN mcp_tool_mappings.openapi_path IS 'The original OpenAPI path template, e.g., /users/{userId}.';
COMMENT ON COLUMN mcp_tool_mappings.http_method IS 'The HTTP method for the endpoint (e.g., POST, GET).';
COMMENT ON COLUMN mcp_tool_mappings.param_mappings IS 'A JSON object mapping MCP properties back to their OpenAPI parameter sources (path, query, header).';
COMMENT ON COLUMN mcp_tool_mappings.body_mapping IS 'A JSON object describing how to reconstruct the original request body.';

-- Profile and Role definition tables
CREATE TABLE profiles (
                          id BIGSERIAL PRIMARY KEY,
                          name TEXT NOT NULL,
                          description TEXT,
                          path_segment TEXT NOT NULL,
                          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_profile_name ON profiles (name);
CREATE UNIQUE INDEX idx_profile_path_segment ON profiles (path_segment);
COMMENT ON TABLE profiles IS 'Profiles are groups of MCP tools.';
COMMENT ON COLUMN profiles.name IS 'The unique name for the profile, e.g., "billing_admin_tools".';
COMMENT ON COLUMN profiles.description IS 'A description of what this profile contains.';
COMMENT ON COLUMN profiles.path_segment IS 'A unique string for the URL path, e.g., "billing".';

CREATE TABLE roles (
                       id BIGSERIAL PRIMARY KEY,
                       name TEXT NOT NULL,
                       created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                       updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
CREATE UNIQUE INDEX idx_role_name ON roles (name);
COMMENT ON TABLE roles IS 'Roles define user roles within the system.';
COMMENT ON COLUMN roles.name IS 'The unique name for the role, e.g., "admin", "viewer".';

-- User table
CREATE TABLE users (
                       id BIGSERIAL PRIMARY KEY,
                       username TEXT NOT NULL UNIQUE,
                       password_hash TEXT NOT NULL,
                       created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                       updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
COMMENT ON COLUMN users.username IS 'The unique username for the user.';
COMMENT ON COLUMN users.password_hash IS 'Hashed password for the user.';

-- Join tables for relationships
CREATE TABLE profile_tools (
                               profile_id BIGINT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
                               tool_id BIGINT NOT NULL REFERENCES mcp_tools(id) ON DELETE CASCADE,
                               acl TEXT NOT NULL, -- e.g., 'EXECUTE', 'READ_ONLY', 'DENY'
                               PRIMARY KEY (profile_id, tool_id)
);
COMMENT ON TABLE profile_tools IS 'Maps tools to profiles with an Access Control List (ACL).';
COMMENT ON COLUMN profile_tools.acl IS 'Access control level for the tool in this profile.';

CREATE TABLE role_profiles (
                               role_id BIGINT NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
                               profile_id BIGINT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
                               PRIMARY KEY (role_id, profile_id)
);
COMMENT ON TABLE role_profiles IS 'Maps which roles have access to which profiles.';

CREATE TABLE user_roles (
                            user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                            role_id BIGINT NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
                            PRIMARY KEY (user_id, role_id)
);
COMMENT ON TABLE user_roles IS 'Maps users to roles.';


-- +goose Down
-- Drop all tables in the reverse order of creation to avoid foreign key errors.
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS role_profiles CASCADE;
DROP TABLE IF EXISTS profile_tools CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;
DROP TABLE IF EXISTS mcp_tool_mappings CASCADE;
DROP TABLE IF EXISTS mcp_tools CASCADE;
