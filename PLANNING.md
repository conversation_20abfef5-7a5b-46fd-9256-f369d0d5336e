# SUSE AIR - Planning & Architecture

## Project Overview

SUSE AIR is a comprehensive Go application that converts OpenAPI v3 specifications into MCP (Model Context Protocol) tools and provides a complete ecosystem for managing and executing them. The project includes:

- **OpenAPI to MCP Conversion**: Automated conversion of REST APIs to MCP tools
- **MCP Server**: Full MCP protocol-compliant server with authentication
- **Profile Management**: Multi-tenant tool organization and access control
- **Interactive TUI**: Terminal user interface for management operations
- **CLI Tools**: Command-line interface for all operations
- **User & Role Management**: Authentication and authorization system

The project bridges OpenAPI specifications with MCP tool execution by storing conversion mappings in a PostgreSQL database and providing secure, multi-tenant access through profiles.

## Key OpenAPI to MCP Conversion Challenges

Based on research from the field, several important considerations apply to this conversion process:

### Schema Transformation Complexity
- **Parameter Flattening**: OpenAPI endpoints combine request body, path parameters, query parameters, and headers into a single MCP tool schema
- **Root Object Requirement**: MCP tools require object-type root schemas, but OpenAPI can have primitive/array request bodies. The converter uses a "wrapped" strategy for non-object bodies
- **Naming Collision Handling**: When combining multiple parameter sources, automatic collision resolution is needed

### Reference Resolution Strategy
- **$ref Inlining**: The converter resolves `$ref` references from `#/components/schemas` into self-contained schemas
- **Circular Reference Detection**: Prevents infinite recursion during schema resolution with visited tracking
- **Schema Simplification**: Complex nested references are flattened to improve LLM comprehension

### Execution Context Considerations
- **Bidirectional Mapping**: The system maintains mappings to reconstruct original OpenAPI requests from MCP tool arguments
- **Parameter Distribution**: MCP arguments are correctly distributed back to path, query, header, and body locations
- **Request Body Reconstruction**: Supports both "wrapped" (single property) and "merged" (object composition) strategies

## Commands

### Building and Running
```bash
# Build the application
go build -o air ./cmd/air

# Start MCP Server (serves MCP protocol endpoints)
./air serve --port 8080 --db "postgres://user:password@localhost:5432/sairdev?sslmode=disable"

# Start Interactive TUI (terminal user interface)
./air tui --db "postgres://user:password@localhost:5432/sairdev?sslmode=disable"

# Convert OpenAPI spec to MCP tools
./air convert -f path/to/openapi.json --db "postgres://user:password@localhost:5432/sairdev?sslmode=disable"

# Profile Management
./air profile create --name "my-profile" --description "My API profile" --path-segment "my-api" --db "..."
./air profile list --db "..."
./air profile update --name "my-profile" --description "Updated description" --db "..."
./air profile delete --name "my-profile" --db "..."

# Tool Management
./air tool list --profile "my-profile" --db "..."
./air tool associate --profile "my-profile" --tool "MyTool" --acl "EXECUTE" --db "..."

# User Management
./air user create --username "alice" --password "secret123" --db "..."
./air user list --db "..."
```

### Database Operations
```bash
# Install dependencies (includes goose for migrations)
make deps

# Run database migrations using goose
make migrate-dev  # Uses default dev database URL
# Or manually: cd sql/migrations && goose postgres "postgres://..." up

# Generate Go code from SQL using sqlc
cd sql && sqlc generate

# Reset database (drops and recreates all tables)
make migrate-reset
```

### Dependencies
```bash
# Install all dependencies including dev tools
make deps

# Standard Go dependency management
go mod tidy
go mod download
```

## Architecture

### Core Components

- **cmd/air/main.go**: Main entry point with CLI commands using urfave/cli
- **internal/cmd/commands.go**: All CLI command implementations (serve, tui, convert, profile, tool, user)
- **internal/server/**: MCP server implementation with full protocol compliance
  - **mcp.go**: MCP protocol handlers (initialize, tools/list, tools/call)
  - **auth.go**: JWT authentication middleware with audience validation
  - **auth_server.go**: OAuth2-style authorization server for token generation
- **internal/tui/**: Terminal User Interface using Bubble Tea framework
  - **main.go**: Main TUI model and navigation
  - **profile_model.go**: Profile management interface
  - **tool_model.go**: Tool association interface
  - **user_model.go**: User management interface
- **internal/schema/**: OpenAPI to MCP conversion logic
  - **converter.go**: Converts OpenAPI specs to MCP tools with mapping generation
  - **executor.go**: Rebuilds HTTP requests from MCP tool arguments using stored mappings
- **internal/db/**: Generated database layer using sqlc for PostgreSQL operations
- **sql/**: Database schema and queries
  - **migrations/**: Goose migration files
  - **query/**: SQL queries for sqlc generation

### Key Data Flows

1. **OpenAPI Conversion**: OpenAPI JSON → Schema conversion → Database storage (tools + mappings)
2. **MCP Server**: Client request → Profile lookup → Tool execution → HTTP API call → Response
3. **TUI Management**: User interaction → Database operations → Real-time feedback
4. **Authentication**: Token request → JWT generation → Audience validation → Access control

### Database Schema

Complete PostgreSQL schema with the following tables:
- **`profiles`**: Multi-tenant organization units with path segments for MCP endpoints
- **`mcp_tools`**: Generated MCP tool definitions with JSON schemas
- **`mcp_tool_mappings`**: Conversion mappings to reconstruct original OpenAPI requests
- **`profile_tools`**: Many-to-many association between profiles and tools with ACL rules
- **`users`**: User accounts with bcrypt password hashing
- **`roles`**: Role-based access control definitions
- **`user_roles`**: Many-to-many association between users and roles

### Schema Resolution

The converter handles OpenAPI `$ref` resolution with circular reference detection, inlining referenced schemas from `#/components/schemas`.

### Request Reconstruction

Two body mapping strategies:
- **Wrapped**: Single MCP property contains entire request body
- **Merged**: Multiple MCP properties compose the request body object

### Tool Generation

Uses `github.com/iancoleman/strcase` for consistent camelCase tool naming from OpenAPI operationId or generated names.

## MCP Server Features

### Protocol Compliance
- **Full MCP Protocol Support**: Implements MCP specification version 2025-03-26
- **Standard Methods**: `initialize`, `tools/list`, `tools/call` with proper JSON-RPC 2.0 format
- **Capability Negotiation**: Advertises `tools.listChanged` capability
- **Error Handling**: Proper JSON-RPC error responses with detailed messages

### Authentication & Security
- **JWT Authentication**: RFC 7519 compliant JSON Web Tokens
- **Audience Validation**: RFC 8707 compliant audience (`aud`) claim validation
- **Profile-Specific Tokens**: Tokens scoped to specific profile path segments
- **Secure Token Generation**: OAuth2-style authorization flow with proper expiration

### Multi-Tenant Architecture
- **Profile-Based Isolation**: Each profile has its own MCP endpoint (`/mcp/{profile}`)
- **Tool Association**: Flexible many-to-many relationship between profiles and tools
- **Access Control Lists**: Per-tool ACL rules (EXECUTE, READ_ONLY, DENY)
- **Path Segment Routing**: Clean URL routing based on profile path segments

### MCP Inspector Compatibility
- **Official Tool Support**: Tested with `@modelcontextprotocol/inspector`
- **CORS Headers**: Proper cross-origin resource sharing for web-based testing
- **Development Mode**: Authentication can be disabled for testing
- **Real-time Testing**: Live tool execution and schema validation

## Terminal User Interface (TUI)

### Bubble Tea Framework
- **Modern TUI**: Built with Charm's Bubble Tea for responsive terminal interfaces
- **Model-View-Update**: Clean architecture with proper state management
- **Keyboard Navigation**: Intuitive arrow key and tab-based navigation
- **Real-time Updates**: Live database synchronization with user feedback

### Management Interfaces
- **Profile Management**: Create, list, update, delete profiles with validation
- **Tool Association**: Associate/disassociate tools with profiles and set ACL rules
- **User Management**: Create users, assign roles, manage permissions
- **Status Messages**: Clear success/error feedback for all operations

### Error Handling
- **Comprehensive Error Display**: User-friendly error messages with context
- **Input Validation**: Real-time validation of form inputs
- **Database Error Handling**: Graceful handling of database connection issues
- **Recovery Mechanisms**: Ability to retry failed operations

## Testing

### Bubble Tea

The main insight was understanding that Bubble Tea uses a command/message pattern where:

- User interactions generate messages
- Models process messages and may return commands
- Commands are executed asynchronously and generate new messages
- This cycle continues until the program exits

The TUI tests needed to properly simulate this flow by:

- Sending key messages to models
- Executing any returned commands
- Processing the messages those commands generate
- Repeating until the desired state is reached

### Running Tests
```bash
# All tests (requires Docker for integration tests)
make test

# Unit tests only (no external dependencies)
make test-unit

# Integration tests only (requires Docker or TEST_DATABASE_URL)
make test-integration

# Tests with coverage report
make test-coverage

# Skip database tests entirely
make test-no-docker

# TUI-specific tests
go test ./tests/unit/tui/... -v

# Server authentication tests
go test ./tests/unit/server/... -v
```

### Test Structure
- **Test Readme**: `tests/README.md` - Detailed instructions and test categories
- **Unit Tests**: `tests/unit/` - Test individual components without external dependencies
  - **TUI Tests**: Complete Bubble Tea model testing with mock databases
  - **Server Tests**: Authentication middleware and MCP protocol testing
  - **Schema Tests**: OpenAPI conversion and validation testing
- **Integration Tests**: `tests/integration/` - End-to-end testing with real PostgreSQL
  - **CLI Integration**: Full command-line interface testing
  - **Database Integration**: Complete CRUD operations testing
  - **TUI Integration**: Terminal interface with real database testing
- **Test Data**: `tests/testdata/` - Sample OpenAPI specifications for testing conversion logic

### Test Coverage
- **TUI Models**: 100% coverage of profile, tool, and user management interfaces
- **Authentication**: Complete JWT and audience validation testing
- **Database Operations**: Full CRUD testing for all entities
- **MCP Protocol**: Complete protocol compliance verification
- **Error Handling**: Comprehensive error scenario testing

### Database Testing
Integration tests use dockertest to automatically create PostgreSQL containers. Set `TEST_DATABASE_URL` to use an existing database or `SKIP_DB_TESTS=true` to skip database tests entirely.

## Development Workflow

### Setting Up Development Environment
```bash
# Clone repository
git clone <repository-url>
cd suse-air

# Install dependencies
make deps

# Start development database
make dev-db

# Run migrations
make migrate-dev

# Build application
make build

# Run tests
make test
```

### Code Generation
```bash
# Regenerate database code after SQL changes
cd sql && sqlc generate

# Regenerate after adding new queries
make generate
```

### MCP Server Testing
```bash
# Start server with test data
./air serve --port 8081 --db "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"

# Test with MCP Inspector
npx @modelcontextprotocol/inspector http://localhost:8081/mcp/test-profile

# Manual protocol testing
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' \
  http://localhost:8081/mcp/test-profile
```

## Key Implementation Details

### Authentication Flow
1. Client requests token from `/auth` endpoint
2. Server generates JWT with profile-specific audience claim
3. Client includes token in `Authorization: Bearer <token>` header
4. Server validates token signature and audience claim
5. Request is routed to appropriate profile handler

### Profile Routing
1. Request URL `/mcp/{profile}` is parsed to extract profile path segment
2. Profile is looked up in database by path segment
3. Profile name is used to query associated tools
4. Tools are returned with proper MCP protocol formatting

### Tool Execution
1. MCP `tools/call` request specifies tool name and arguments
2. Tool mapping is retrieved from database
3. Arguments are distributed to path, query, header, and body parameters
4. HTTP request is constructed and executed against target API
5. Response is formatted as MCP protocol response

### Error Handling Strategy
- **Database Errors**: Graceful degradation with user-friendly messages
- **Authentication Errors**: Clear HTTP status codes (401, 403)
- **Validation Errors**: Detailed field-level error reporting
- **Network Errors**: Timeout handling and retry mechanisms
- **Protocol Errors**: Proper JSON-RPC error responses

## Phase 5 Implementation Insights & Best Practices

### Connection Management & Security

#### Encryption Best Practices
- **AES-GCM Encryption**: Use AES-GCM for encrypting sensitive data (passwords, tokens, API keys)
- **Environment-based Keys**: Store encryption keys in environment variables (`SAIR_ENCRYPTION_KEY`)
- **Default Key Warning**: Always warn users when using default/development encryption keys
- **Nonce Generation**: Use crypto/rand for generating unique nonces for each encryption operation
- **Base64 Encoding**: Store encrypted data as base64 for database compatibility

```go
// Good: Proper encryption with unique nonce per operation
nonce := make([]byte, gcm.NonceSize())
io.ReadFull(rand.Reader, nonce)
ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
```

#### Database Schema Design for Connections
- **Separate Encrypted Fields**: Store each auth type's secrets in separate encrypted columns
- **Auth Type Validation**: Use CHECK constraints to validate auth types at database level
- **Foreign Key Relationships**: Link operations to schemas, and schemas to connections via join tables
- **Default Connection Support**: Use boolean flags with partial indexes for default connections

```sql
-- Good: Proper constraint and indexing
CREATE INDEX idx_schema_connections_default
ON schema_connections (schema_id, is_default)
WHERE is_default = TRUE;
```

### CLI Command Design Patterns

#### Consistent Flag Naming
- **Standard Flags**: Use consistent flag names across commands (`--name`, `--db`, `--description`)
- **Required vs Optional**: Mark required flags explicitly in CLI framework
- **Environment Variables**: Support environment variables for common flags (DATABASE_URL)
- **Validation Order**: Validate required fields first, then business logic

#### Error Message Quality
- **Specific Validation**: Provide specific error messages for each validation failure
- **Context Information**: Include the invalid value in error messages when safe
- **Actionable Guidance**: Tell users what they need to do to fix the error

```go
// Good: Specific, actionable error message
if authType == "basic" && (username == "" || secret == "") {
    return fmt.Errorf("username and secret are required for basic auth")
}
```

### TUI (Terminal User Interface) Best Practices

#### State Management
- **Clear State Enum**: Use explicit state enums for different TUI screens
- **State Transitions**: Handle state transitions explicitly in update methods
- **Input Focus Management**: Properly manage focus between input fields
- **Escape Key Handling**: Provide consistent escape key behavior for navigation

#### Form Design Patterns
- **Dynamic Forms**: Adapt form fields based on user selections (auth type)
- **Tab Navigation**: Implement tab navigation between form fields
- **Input Validation**: Validate inputs in real-time and show helpful messages
- **Sensitive Data**: Use password input mode for sensitive fields

```go
// Good: Dynamic form adaptation
switch authType {
case "basic":
    cm.usernameInput.Focus()
case "bearer":
    cm.secretInput.Focus()
case "custom_header":
    cm.headerNameInput.Focus()
}
```

#### User Experience Considerations
- **Status Messages**: Show clear success/error messages for all operations
- **Loading States**: Indicate when operations are in progress
- **Keyboard Shortcuts**: Provide intuitive keyboard shortcuts (n=new, d=delete)
- **Help Text**: Include help text for available commands

### Testing Strategies

#### Integration Test Patterns
- **Database Setup**: Use dockertest for consistent database testing environments
- **Test Isolation**: Clean up database state between tests
- **Service Layer Testing**: Test business logic through service layer, not just database queries
- **Error Scenario Testing**: Test both success and failure scenarios

#### TUI Testing Challenges
- **Complex Interaction**: TUI interactions are complex to test programmatically
- **Service Layer Focus**: Focus tests on underlying service layer functionality
- **Mock vs Real Database**: Use real databases for integration tests, mocks for unit tests
- **State Verification**: Verify TUI state through model inspection rather than view output

### Code Organization Lessons

#### Type Assertions in TUI
- **Interface vs Concrete Types**: Services often need concrete types, but TUI uses interfaces
- **Safe Type Assertions**: Always check type assertion success and handle failures gracefully
- **Panic vs Error**: Use panic for programming errors, return errors for runtime issues

```go
// Good: Safe type assertion with error handling
dbQueries, ok := queries.(*db.Queries)
if !ok {
    return fmt.Errorf("queries must be of type *db.Queries")
}
```

#### SQLC Integration Patterns
- **Parameter Naming**: Keep SQL parameter names consistent with Go field names
- **Nullable Fields**: Use pgtype for nullable database fields
- **Query Organization**: Group related queries in separate SQL files
- **Generated Code**: Regenerate SQLC code after schema changes

### Authentication Integration

#### Multi-Auth Support
- **Pluggable Auth**: Design auth system to support multiple authentication types
- **Request Building**: Separate request building from authentication logic
- **Header Management**: Use standard HTTP header patterns for different auth types
- **Secret Management**: Never log or expose secrets in error messages

#### Connection-Aware Execution
- **Dynamic Base URLs**: Support different base URLs per connection
- **Auth Context**: Pass authentication context through execution pipeline
- **Error Propagation**: Distinguish between auth errors and API errors
- **Fallback Handling**: Gracefully handle missing or invalid connections

### Performance Considerations

#### Database Query Optimization
- **Join Queries**: Use joins to fetch related data in single queries
- **Index Strategy**: Create indexes for foreign key relationships and frequent lookups
- **Connection Pooling**: Use connection pooling for database access
- **Query Caching**: Consider caching for frequently accessed, rarely changed data

#### Memory Management
- **Encryption Overhead**: Be aware of memory overhead from encryption operations
- **Large Payloads**: Handle large API responses efficiently
- **Connection Reuse**: Reuse HTTP connections for better performance

### Security Considerations

#### Secrets Management
- **Encryption at Rest**: Always encrypt sensitive data in database
- **Memory Clearing**: Clear sensitive data from memory after use
- **Audit Logging**: Log access to sensitive operations (without exposing secrets)
- **Principle of Least Privilege**: Only decrypt secrets when needed for execution

#### Input Validation
- **Server-Side Validation**: Never trust client-side validation alone
- **SQL Injection Prevention**: Use parameterized queries (SQLC handles this)
- **XSS Prevention**: Sanitize any user input displayed in TUI/CLI
- **Path Traversal**: Validate file paths and schema names

### Common Pitfalls to Avoid

1. **Unused Variables**: Go compiler is strict about unused variables in tests
2. **Import Management**: Remove unused imports to avoid compilation errors
3. **Type Assertion Failures**: Always handle type assertion failures gracefully
4. **Database Migration Order**: Consider foreign key constraints when ordering migrations
5. **Test Database Cleanup**: Always clean up test databases to avoid state pollution
6. **Error Message Consistency**: Keep error messages consistent across CLI and TUI
7. **Sensitive Data Logging**: Never log passwords, tokens, or other secrets
8. **State Management**: Keep TUI state management simple and predictable

### Future Enhancement Opportunities

- **Connection Testing**: Add "test connection" functionality to verify connectivity
- **Bulk Operations**: Support bulk import/export of connections
- **Connection Templates**: Provide templates for common API providers
- **Audit Trail**: Track connection usage and modifications
- **Connection Sharing**: Allow connections to be shared between users/profiles
- **Advanced Auth**: Support OAuth2, API key rotation, and other advanced auth methods

