package integration

import (
	"context"
	"testing"

	"github.com/charmbracelet/bubbletea"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/tui"
	"github.com/ravan/suse-air/internal/util"
)

func TestConnectionTUIIntegration(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	queries := testDB.GetConnection(t)

	// Test TUI Connection Model with real database
	t.Run("TUIConnectionModelIntegration", func(t *testing.T) {
		// Initialize TUI model with real database
		model := tui.InitialModelWithQueries(queries)
		tuiModel := model.(tui.Model)

		// Verify initial state includes Connection Management
		assert.Contains(t, tuiModel.View(), "Welcome to SUSE AIR TUI!")
		assert.Contains(t, tuiModel.View(), "Connection Management")

		// Navigate to Connection Management
		// Simulate key presses to navigate to connection management
		updatedModel, _ := tuiModel.Update(tea.KeyMsg{Type: tea.KeyDown}) // Move to Tool Association
		updatedModel, _ = updatedModel.Update(tea.KeyMsg{Type: tea.KeyDown}) // Move to User Management
		updatedModel, _ = updatedModel.Update(tea.KeyMsg{Type: tea.KeyDown}) // Move to Role Management
		updatedModel, _ = updatedModel.Update(tea.KeyMsg{Type: tea.KeyDown}) // Move to Connection Management
		updatedModel, _ = updatedModel.Update(tea.KeyMsg{Type: tea.KeyEnter}) // Select Connection Management

		// Verify we're in connection management view
		view := updatedModel.View()
		assert.Contains(t, view, "Connection Management")
		// Note: The actual TUI navigation is complex to test, so we just verify
		// that the connection management option is available in the main menu
	})
}

func TestConnectionCRUDThroughTUI(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test connection CRUD operations through the service layer
	// (Since TUI interaction is complex to test, we test the underlying services)
	t.Run("ConnectionServiceIntegration", func(t *testing.T) {
		connService := models.NewConnectionService(queries)

		// Test 1: Create Connection
		req := models.CreateConnectionRequest{
			Name:        "tui-test-connection",
			Description: "TUI test connection",
			BaseURL:     "https://api.tui-test.com",
			Auth: util.ConnectionAuth{
				Type:  "bearer",
				Token: "tui-test-token",
			},
		}

		createdConn, err := connService.CreateConnection(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "tui-test-connection", createdConn.Name)
		assert.Equal(t, "TUI test connection", createdConn.Description)
		assert.Equal(t, "https://api.tui-test.com", createdConn.BaseURL)
		assert.Equal(t, "bearer", createdConn.Auth.Type)
		assert.Equal(t, "tui-test-token", createdConn.Auth.Token)

		// Test 2: List Connections
		connections, err := connService.ListConnections(ctx)
		require.NoError(t, err)
		assert.Len(t, connections, 1)
		assert.Equal(t, "tui-test-connection", connections[0].Name)
		// Sensitive data should not be in list view
		assert.Empty(t, connections[0].Auth.Token)

		// Test 3: Get Connection (with sensitive data)
		retrievedConn, err := connService.GetConnection(ctx, "tui-test-connection")
		require.NoError(t, err)
		assert.Equal(t, "tui-test-connection", retrievedConn.Name)
		assert.Equal(t, "tui-test-token", retrievedConn.Auth.Token)

		// Test 4: Delete Connection
		err = connService.DeleteConnection(ctx, "tui-test-connection")
		require.NoError(t, err)

		// Verify deletion
		connections, err = connService.ListConnections(ctx)
		require.NoError(t, err)
		assert.Len(t, connections, 0)
	})
}

func TestSchemaAssociationThroughTUI(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test schema association operations through the service layer
	t.Run("SchemaAssociationServiceIntegration", func(t *testing.T) {
		connService := models.NewConnectionService(queries)
		schemaService := models.NewSchemaService(queries)

		// Create test connection
		connReq := models.CreateConnectionRequest{
			Name:    "schema-test-connection",
			BaseURL: "https://api.schema-test.com",
			Auth: util.ConnectionAuth{
				Type:  "bearer",
				Token: "schema-test-token",
			},
		}
		_, err := connService.CreateConnection(ctx, connReq)
		require.NoError(t, err)

		// Create test schema
		schemaReq := models.CreateSchemaRequest{
			Name:        "schema-test-schema",
			Description: "TUI test schema",
			Filename:    "test-schema.json",
			Version:     "1.0.0",
		}
		_, err = schemaService.CreateSchema(ctx, schemaReq)
		require.NoError(t, err)

		// Test 1: Associate Connection with Schema
		err = schemaService.AssociateConnection(ctx, "schema-test-schema", "schema-test-connection", true)
		require.NoError(t, err)

		// Verify association
		schema, err := queries.GetOpenAPISchemaByName(ctx, "schema-test-schema")
		require.NoError(t, err)

		connections, err := queries.ListConnectionsBySchema(ctx, schema.ID)
		require.NoError(t, err)
		assert.Len(t, connections, 1)
		assert.Equal(t, "schema-test-connection", connections[0].Name)

		// Test 2: Disassociate Connection from Schema
		err = schemaService.DisassociateConnection(ctx, "schema-test-schema", "schema-test-connection")
		require.NoError(t, err)

		// Verify disassociation
		connections, err = queries.ListConnectionsBySchema(ctx, schema.ID)
		require.NoError(t, err)
		assert.Len(t, connections, 0)
	})
}

func TestConnectionTUIValidation(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test validation through the service layer
	t.Run("ConnectionValidation", func(t *testing.T) {
		connService := models.NewConnectionService(queries)

		// Test 1: Invalid auth type
		req := models.CreateConnectionRequest{
			Name:    "invalid-auth-connection",
			BaseURL: "https://api.test.com",
			Auth: util.ConnectionAuth{
				Type: "invalid-auth-type",
			},
		}

		_, err := connService.CreateConnection(ctx, req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid auth type")

		// Test 2: Basic auth validation
		req = models.CreateConnectionRequest{
			Name:    "basic-auth-connection",
			BaseURL: "https://api.test.com",
			Auth: util.ConnectionAuth{
				Type:     "basic",
				Username: "testuser",
				Password: "testpass",
			},
		}

		_, err = connService.CreateConnection(ctx, req)
		require.NoError(t, err)

		// Test 3: Bearer auth validation
		req = models.CreateConnectionRequest{
			Name:    "bearer-auth-connection",
			BaseURL: "https://api.test.com",
			Auth: util.ConnectionAuth{
				Type:  "bearer",
				Token: "test-token",
			},
		}

		_, err = connService.CreateConnection(ctx, req)
		require.NoError(t, err)

		// Test 4: Custom header auth validation
		req = models.CreateConnectionRequest{
			Name:    "custom-header-connection",
			BaseURL: "https://api.test.com",
			Auth: util.ConnectionAuth{
				Type:        "custom_header",
				HeaderName:  "X-API-Key",
				HeaderValue: "custom-key",
			},
		}

		_, err = connService.CreateConnection(ctx, req)
		require.NoError(t, err)

		// Verify all connections were created
		connections, err := connService.ListConnections(ctx)
		require.NoError(t, err)
		assert.Len(t, connections, 3)
	})
}

func TestConnectionTUIErrorHandling(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test error handling through the service layer
	t.Run("ErrorHandling", func(t *testing.T) {
		connService := models.NewConnectionService(queries)
		schemaService := models.NewSchemaService(queries)

		// Test 1: Get non-existent connection
		_, err := connService.GetConnection(ctx, "non-existent-connection")
		assert.Error(t, err)

		// Test 2: Delete non-existent connection
		err = connService.DeleteConnection(ctx, "non-existent-connection")
		// Note: The current implementation may not return an error for non-existent connections
		// This is acceptable behavior for delete operations (idempotent)

		// Test 3: Associate with non-existent schema
		err = schemaService.AssociateConnection(ctx, "non-existent-schema", "non-existent-connection", true)
		assert.Error(t, err)

		// Test 4: Associate with non-existent connection
		// First create a schema
		schemaReq := models.CreateSchemaRequest{
			Name:     "test-schema",
			Filename: "test.json",
		}
		_, err = schemaService.CreateSchema(ctx, schemaReq)
		require.NoError(t, err)

		err = schemaService.AssociateConnection(ctx, "test-schema", "non-existent-connection", true)
		assert.Error(t, err)
	})
}
