package unit_test

import (
	"context"
	"testing"
	"time"

	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/tui"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockDBQueries is a mock implementation of db.Queries
type MockDBQueries struct {
	mock.Mock
}

func (m *MockDBQueries) CreateProfile(ctx context.Context, arg db.CreateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListProfiles(ctx context.Context) ([]db.Profile, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByName(ctx context.Context, name string) (db.Profile, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByID(ctx context.Context, id int64) (db.Profile, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) UpdateProfile(ctx context.Context, arg db.UpdateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) DeleteProfile(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) GetMCPToolByName(ctx context.Context, name string) (db.McpTool, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) GetMCPToolByID(ctx context.Context, id int64) (db.McpTool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) ListMCPToolsByProfile(ctx context.Context, profileName string) ([]db.ListMCPToolsByProfileRow, error) {
	args := m.Called(ctx, profileName)
	return args.Get(0).([]db.ListMCPToolsByProfileRow), args.Error(1)
}

func (m *MockDBQueries) CreateProfileTool(ctx context.Context, arg db.CreateProfileToolParams) (db.ProfileTool, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.ProfileTool), args.Error(1)
}

func (m *MockDBQueries) DeleteProfileTool(ctx context.Context, arg db.DeleteProfileToolParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) CreateUser(ctx context.Context, arg db.CreateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) ListUsers(ctx context.Context) ([]db.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.User), args.Error(1)
}

func (m *MockDBQueries) GetUserByUsername(ctx context.Context, username string) (db.User, error) {
	args := m.Called(ctx, username)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) GetRoleByName(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) CreateUserRole(ctx context.Context, arg db.CreateUserRoleParams) (db.UserRole, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.UserRole), args.Error(1)
}

func (m *MockDBQueries) DeleteUserRole(ctx context.Context, arg db.DeleteUserRoleParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

// Additional methods to implement the full Querier interface
func (m *MockDBQueries) CreateMCPTool(ctx context.Context, arg db.CreateMCPToolParams) (db.McpTool, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) CreateMCPToolMapping(ctx context.Context, arg db.CreateMCPToolMappingParams) (db.McpToolMapping, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.McpToolMapping), args.Error(1)
}

func (m *MockDBQueries) CreateRole(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) GetToolWithMapping(ctx context.Context, toolName string) (db.GetToolWithMappingRow, error) {
	args := m.Called(ctx, toolName)
	return args.Get(0).(db.GetToolWithMappingRow), args.Error(1)
}

func (m *MockDBQueries) ListMCPTools(ctx context.Context) ([]db.McpTool, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.McpTool), args.Error(1)
}

func (m *MockDBQueries) CreateRoleProfile(ctx context.Context, arg db.CreateRoleProfileParams) (db.RoleProfile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.RoleProfile), args.Error(1)
}

func (m *MockDBQueries) DeleteRoleProfile(ctx context.Context, arg db.DeleteRoleProfileParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) GetRoleByID(ctx context.Context, id int64) (db.Role, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) GetProfileByPathSegment(ctx context.Context, pathSegment string) (db.Profile, error) {
	args := m.Called(ctx, pathSegment)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListRoles(ctx context.Context) ([]db.Role, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) ListProfilesByRole(ctx context.Context, roleID int64) ([]db.Profile, error) {
	args := m.Called(ctx, roleID)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListRolesByUser(ctx context.Context, userID int64) ([]db.Role, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) UpdateUser(ctx context.Context, arg db.UpdateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) UpdateRole(ctx context.Context, arg db.UpdateRoleParams) (db.Role, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) DeleteUser(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) DeleteRole(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// TestMainMenuNavigation tests basic main menu navigation
func TestMainMenuNavigation(t *testing.T) {
	mockQueries := new(MockDBQueries)
	m := tui.InitialModelWithQueries(mockQueries).(tui.Model)

	// Test initial view
	assert.Contains(t, m.View(), "Welcome to SUSE AIR TUI!")
	assert.Contains(t, m.View(), "> Profile Management")

	// Move down
	updatedModel, cmd := m.Update(tea.KeyMsg{Type: tea.KeyDown})
	m = updatedModel.(tui.Model)
	assert.Nil(t, cmd)
	assert.Contains(t, m.View(), "> Tool Association")

	// Move up
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyUp})
	m = updatedModel.(tui.Model)
	assert.Nil(t, cmd)
	assert.Contains(t, m.View(), "> Profile Management")

	// Select Profile Management
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyEnter})
	m = updatedModel.(tui.Model)
	assert.NotNil(t, cmd)

	// Execute the command to get the selectMenuItemMsg
	if cmd != nil {
		msg := cmd()
		updatedModel, cmd = m.Update(msg)
		m = updatedModel.(tui.Model)
	}
	assert.Contains(t, m.View(), "Profile Management")

	// Go back to main menu
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyEsc})
	m = updatedModel.(tui.Model)
	assert.Nil(t, cmd)
	assert.Contains(t, m.View(), "Welcome to SUSE AIR TUI!")
}

// TestProfileList tests listing profiles
func TestProfileList(t *testing.T) {
	mockQueries := new(MockDBQueries)
	expectedProfiles := []db.Profile{
		{ID: 1, Name: "profile1", Description: pgtype.Text{String: "Desc1", Valid: true}, PathSegment: "path1"},
		{ID: 2, Name: "profile2", Description: pgtype.Text{String: "Desc2", Valid: true}, PathSegment: "path2"},
	}
	mockQueries.On("ListProfiles", mock.Anything).Return(expectedProfiles, nil).Once()

	m := tui.InitialModelWithQueries(mockQueries)
	// Navigate to profile view
	var cmd tea.Cmd
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyEnter}) // Select Profile Management

	// Execute the command to get the selectMenuItemMsg
	if cmd != nil {
		msg := cmd()
		m, cmd = m.Update(msg)
	}

	// The profile model Init() should have returned a fetchProfilesCmd
	// Execute this command to trigger the ListProfiles call
	if cmd != nil {
		msg := cmd() // This should call ListProfiles and return profilesFetchedMsg
		var updatedModel tea.Model
		updatedModel, cmd = m.Update(msg) // Send the profilesFetchedMsg to the model
		m = updatedModel.(tui.Model)
	}

	assert.Contains(t, m.View(), "Profiles:")
	assert.Contains(t, m.View(), "profile1 (Path: path1)")
	assert.Contains(t, m.View(), "profile2 (Path: path2)")
	mockQueries.AssertExpectations(t)
}

// TestProfileCreate tests creating a profile
func TestProfileCreate(t *testing.T) {
	mockQueries := new(MockDBQueries)
	createdProfile := db.Profile{
		ID:          1,
		Name:        "newprofile",  // Fix: match actual input
		Description: pgtype.Text{String: "New Desc", Valid: true},
		PathSegment: "new-path",
	}

	// Mock the initial ListProfiles call (empty list)
	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{}, nil).Once()

	mockQueries.On("CreateProfile", mock.Anything, db.CreateProfileParams{
		Name:        "newprofile",  // Fix: actual input without dash
		Description: pgtype.Text{String: "New Desc", Valid: true},
		PathSegment: "new-path",
	}).Return(createdProfile, nil).Once()
	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{createdProfile}, nil).Once()

	m := tui.InitialModelWithQueries(mockQueries)
	// Navigate to profile view
	updatedModel, cmd := m.Update(tea.KeyMsg{Type: tea.KeyEnter}) // Select Profile Management
	m = updatedModel.(tui.Model)

	// Execute the command to get the selectMenuItemMsg
	if cmd != nil {
		msg := cmd()
		updatedModel, cmd = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Execute the fetchProfilesCmd to get the initial empty list
	if cmd != nil {
		msg := cmd() // This should call ListProfiles and return profilesFetchedMsg
		updatedModel, _ = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Press 'c' to go to create form
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'c'}})
	m = updatedModel.(tui.Model)
	assert.NotNil(t, cmd)
	assert.Contains(t, m.View(), "Create New Profile:")

	// Type name
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'n'}})
	m = updatedModel.(tui.Model)
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m = updatedModel.(tui.Model)
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'w'}})
	m = updatedModel.(tui.Model)
	updatedModel, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'-'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'p'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'r'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'o'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'f'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'i'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'l'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	assert.Contains(t, m.View(), "newprofile")

	// Tab to description
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyTab})
	assert.NotNil(t, cmd)

	// Type description
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'N'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'w'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{' '}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'D'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'s'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'c'}})
	assert.Contains(t, m.View(), "New Desc")

	// Tab to path segment
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyTab})
	assert.NotNil(t, cmd)

	// Type path segment
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'n'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'w'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'-'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'p'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'a'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'t'}})
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'h'}})
	assert.Contains(t, m.View(), "new-path")

	// Submit form (Tab from last field to trigger submission)
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyTab})
	assert.NotNil(t, cmd)

	// Execute the createProfileCmd
	if cmd != nil {
		msg := cmd()
		m, cmd = m.Update(msg)
		// Check status message after profileCreatedMsg but before fetchProfilesCmd
		assert.Contains(t, m.View(), "Profile 'newprofile' created successfully!")
	}

	// Execute the subsequent fetchProfilesCmd
	if cmd != nil {
		msg := cmd()
		m, cmd = m.Update(msg)
	}

	assert.Contains(t, m.View(), "Profiles:")
	mockQueries.AssertExpectations(t)
}

// TestProfileUpdate tests updating a profile
func TestProfileUpdate(t *testing.T) {
	mockQueries := new(MockDBQueries)
	oldProfile := db.Profile{
		ID:          1,
		Name:        "old-profile",
		Description: pgtype.Text{String: "Old Desc", Valid: true},
		PathSegment: "old-path",
		CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}
	updatedProfile := db.Profile{
		ID:          1,
		Name:        "updated-profile",
		Description: pgtype.Text{String: "Updated Desc", Valid: true},
		PathSegment: "updated-path",
		CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}

	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{oldProfile}, nil).Once()
	mockQueries.On("UpdateProfile", mock.Anything, db.UpdateProfileParams{
		ID:          1,
		Name:        "updated-profile",
		Description: pgtype.Text{String: "Updated Desc", Valid: true},
		PathSegment: "updated-path",
	}).Return(updatedProfile, nil).Once()
	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{updatedProfile}, nil).Once()

	m := tui.InitialModelWithQueries(mockQueries)
	// Navigate to profile view and load profiles
	updatedModel, cmd := m.Update(tea.KeyMsg{Type: tea.KeyEnter}) // Select Profile Management
	m = updatedModel.(tui.Model)

	// Execute the command to get the selectMenuItemMsg
	if cmd != nil {
		msg := cmd()
		updatedModel, cmd = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Execute the fetchProfilesCmd to get the profiles
	if cmd != nil {
		msg := cmd() // This should call ListProfiles and return profilesFetchedMsg
		updatedModel, _ = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Select the profile and go to detail view
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyEnter})
	assert.Contains(t, m.View(), "Profile Details for 'old-profile':")

	// Press 'u' to go to update form
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'u'}})
	assert.NotNil(t, cmd)
	assert.Contains(t, m.View(), "Update Profile 'old-profile':")

	// Clear and type new name
	for i := 0; i < len(oldProfile.Name); i++ {
		m, _ = m.Update(tea.KeyMsg{Type: tea.KeyBackspace})
	}
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'u'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'p'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'a'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'t'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'-'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'p'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'r'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'o'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'f'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'i'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'l'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	assert.Contains(t, m.View(), "updated-profile")

	// Tab to description
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyTab})

	// Clear and type new description
	for i := 0; i < len(oldProfile.Description.String); i++ {
		m, _ = m.Update(tea.KeyMsg{Type: tea.KeyBackspace})
	}
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'U'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'p'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'a'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'t'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{' '}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'D'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'s'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'c'}})
	assert.Contains(t, m.View(), "Updated Desc")

	// Tab to path segment
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyTab})

	// Clear and type new path segment
	for i := 0; i < len(oldProfile.PathSegment); i++ {
		m, _ = m.Update(tea.KeyMsg{Type: tea.KeyBackspace})
	}
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'u'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'p'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'a'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'t'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'e'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'-'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'p'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'a'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'t'}})
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'h'}})
	assert.Contains(t, m.View(), "updated-path")

	// Submit form (Tab from last field to trigger submission)
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyTab})
	assert.NotNil(t, cmd)

	// Execute the updateProfileCmd
	if cmd != nil {
		msg := cmd()
		m, cmd = m.Update(msg)
		// Check status message after profileUpdatedMsg but before fetchProfilesCmd
		assert.Contains(t, m.View(), "Profile 'updated-profile' updated successfully!")
	}

	// Execute the subsequent fetchProfilesCmd
	if cmd != nil {
		msg := cmd()
		m, cmd = m.Update(msg)
	}

	assert.Contains(t, m.View(), "Profiles:")
	mockQueries.AssertExpectations(t)
}

// TestProfileDelete tests deleting a profile
func TestProfileDelete(t *testing.T) {
	mockQueries := new(MockDBQueries)
	existingProfile := db.Profile{
		ID:          1,
		Name:        "profile-to-delete",
		Description: pgtype.Text{String: "", Valid: false},
		PathSegment: "path-to-delete",
	}

	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{existingProfile}, nil).Once()
	mockQueries.On("GetProfileByID", mock.Anything, int64(1)).Return(existingProfile, nil).Once()
	mockQueries.On("DeleteProfile", mock.Anything, int64(1)).Return(nil).Once()
	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{}, nil).Once()

	m := tui.InitialModelWithQueries(mockQueries)
	// Navigate to profile view and load profiles
	updatedModel, cmd := m.Update(tea.KeyMsg{Type: tea.KeyEnter}) // Select Profile Management
	m = updatedModel.(tui.Model)

	// Execute the command to get the selectMenuItemMsg
	if cmd != nil {
		msg := cmd()
		updatedModel, cmd = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Execute the fetchProfilesCmd to get the profiles
	if cmd != nil {
		msg := cmd() // This should call ListProfiles and return profilesFetchedMsg
		updatedModel, _ = m.Update(msg)
		m = updatedModel.(tui.Model)
	}

	// Select the profile and go to detail view
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyEnter})
	assert.Contains(t, m.View(), "Profile Details for 'profile-to-delete':")

	// Press 'd' to go to delete confirmation
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}})
	assert.Contains(t, m.View(), "Are you sure you want to delete profile 'profile-to-delete'? (y/N)")

	// Press 'y' to confirm deletion
	m, cmd = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'y'}})
	assert.NotNil(t, cmd)

	// Execute the deleteProfileCmd
	if cmd != nil {
		msg := cmd()
		m, cmd = m.Update(msg)
		// Check status message after profileDeletedMsg but before fetchProfilesCmd
		assert.Contains(t, m.View(), "Profile 'profile-to-delete' deleted successfully!")
	}

	// Execute the subsequent fetchProfilesCmd
	if cmd != nil {
		msg := cmd()
		m, cmd = m.Update(msg)
	}

	assert.Contains(t, m.View(), "No profiles found.")
	mockQueries.AssertExpectations(t)
}
