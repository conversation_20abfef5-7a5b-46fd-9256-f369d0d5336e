package cli

import (
	"context"
	"testing"

	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/tests/unit/test_utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/urfave/cli/v2"
)

func TestRunRoleCreate(t *testing.T) {
	tests := []struct {
		name        string
		roleName    string
		setupMock   func(*test_utils.MockDBQueries)
		expectError bool
		errorMsg    string
	}{
		{
			name:     "successful role creation",
			roleName: "test-role",
			setupMock: func(m *test_utils.MockDBQueries) {
				m.On("CreateRole", mock.Anything, "test-role").Return(db.Role{
					ID:   1,
					Name: "test-role",
				}, nil)
			},
			expectError: false,
		},
		{
			name:     "role creation with database error",
			roleName: "test-role",
			setupMock: func(m *test_utils.MockDBQueries) {
				m.On("CreateRole", mock.Anything, "test-role").Return(db.Role{}, assert.AnError)
			},
			expectError: true,
			errorMsg:    "failed to create role test-role",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockQuerier := &test_utils.MockDBQueries{}
			tt.setupMock(mockQuerier)

			// Create a mock CLI context
			app := &cli.App{
				Flags: []cli.Flag{
					&cli.StringFlag{Name: "name"},
					&cli.StringFlag{Name: "db"},
				},
			}

			set := cli.NewFlagSet("test", 0)
			set.String("name", tt.roleName, "")
			set.String("db", "test-db", "")

			ctx := cli.NewContext(app, set, nil)

			// Mock the database connection
			originalConnectDB := test_utils.MockConnectDB
			test_utils.MockConnectDB = func(connectionString string) (db.Querier, error) {
				return mockQuerier, nil
			}
			defer func() { test_utils.MockConnectDB = originalConnectDB }()

			err := cmd.RunRoleCreate(ctx)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}

			mockQuerier.AssertExpectations(t)
		})
	}
}

func TestRunRoleList(t *testing.T) {
	tests := []struct {
		name        string
		setupMock   func(*test_utils.MockQuerier)
		expectError bool
		errorMsg    string
	}{
		{
			name: "successful role listing",
			setupMock: func(m *test_utils.MockQuerier) {
				m.On("ListRoles", mock.Anything).Return([]db.Role{
					{ID: 1, Name: "admin"},
					{ID: 2, Name: "user"},
				}, nil)
			},
			expectError: false,
		},
		{
			name: "role listing with database error",
			setupMock: func(m *test_utils.MockQuerier) {
				m.On("ListRoles", mock.Anything).Return([]db.Role{}, assert.AnError)
			},
			expectError: true,
			errorMsg:    "failed to list roles",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockQuerier := &test_utils.MockQuerier{}
			tt.setupMock(mockQuerier)

			// Create a mock CLI context
			app := &cli.App{
				Flags: []cli.Flag{
					&cli.StringFlag{Name: "db"},
				},
			}

			set := cli.NewFlagSet("test", 0)
			set.String("db", "test-db", "")

			ctx := cli.NewContext(app, set, nil)

			// Mock the database connection
			originalConnectDB := test_utils.MockConnectDB
			test_utils.MockConnectDB = func(connectionString string) (db.Querier, error) {
				return mockQuerier, nil
			}
			defer func() { test_utils.MockConnectDB = originalConnectDB }()

			err := cmd.RunRoleList(ctx)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}

			mockQuerier.AssertExpectations(t)
		})
	}
}

func TestRunRoleGet(t *testing.T) {
	tests := []struct {
		name        string
		roleName    string
		setupMock   func(*test_utils.MockQuerier)
		expectError bool
		errorMsg    string
	}{
		{
			name:     "successful role get",
			roleName: "admin",
			setupMock: func(m *test_utils.MockQuerier) {
				m.On("GetRoleByName", mock.Anything, "admin").Return(db.Role{
					ID:   1,
					Name: "admin",
				}, nil)
			},
			expectError: false,
		},
		{
			name:     "role get with database error",
			roleName: "nonexistent",
			setupMock: func(m *test_utils.MockQuerier) {
				m.On("GetRoleByName", mock.Anything, "nonexistent").Return(db.Role{}, assert.AnError)
			},
			expectError: true,
			errorMsg:    "failed to get role nonexistent",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockQuerier := &test_utils.MockQuerier{}
			tt.setupMock(mockQuerier)

			// Create a mock CLI context
			app := &cli.App{
				Flags: []cli.Flag{
					&cli.StringFlag{Name: "name"},
					&cli.StringFlag{Name: "db"},
				},
			}

			set := cli.NewFlagSet("test", 0)
			set.String("name", tt.roleName, "")
			set.String("db", "test-db", "")

			ctx := cli.NewContext(app, set, nil)

			// Mock the database connection
			originalConnectDB := test_utils.MockConnectDB
			test_utils.MockConnectDB = func(connectionString string) (db.Querier, error) {
				return mockQuerier, nil
			}
			defer func() { test_utils.MockConnectDB = originalConnectDB }()

			err := cmd.RunRoleGet(ctx)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}

			mockQuerier.AssertExpectations(t)
		})
	}
}

func TestRunRoleUpdate(t *testing.T) {
	tests := []struct {
		name        string
		roleName    string
		newName     string
		setupMock   func(*test_utils.MockQuerier)
		expectError bool
		errorMsg    string
	}{
		{
			name:     "successful role update",
			roleName: "old-role",
			newName:  "new-role",
			setupMock: func(m *test_utils.MockQuerier) {
				m.On("GetRoleByName", mock.Anything, "old-role").Return(db.Role{
					ID:   1,
					Name: "old-role",
				}, nil)
				m.On("UpdateRole", mock.Anything, db.UpdateRoleParams{
					ID:   1,
					Name: "new-role",
				}).Return(db.Role{
					ID:   1,
					Name: "new-role",
				}, nil)
			},
			expectError: false,
		},
		{
			name:     "role update with get error",
			roleName: "nonexistent",
			newName:  "new-role",
			setupMock: func(m *test_utils.MockQuerier) {
				m.On("GetRoleByName", mock.Anything, "nonexistent").Return(db.Role{}, assert.AnError)
			},
			expectError: true,
			errorMsg:    "failed to get role nonexistent",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockQuerier := &test_utils.MockQuerier{}
			tt.setupMock(mockQuerier)

			// Create a mock CLI context
			app := &cli.App{
				Flags: []cli.Flag{
					&cli.StringFlag{Name: "name"},
					&cli.StringFlag{Name: "new-name"},
					&cli.StringFlag{Name: "db"},
				},
			}

			set := cli.NewFlagSet("test", 0)
			set.String("name", tt.roleName, "")
			set.String("new-name", tt.newName, "")
			set.String("db", "test-db", "")

			ctx := cli.NewContext(app, set, nil)

			// Mock the database connection
			originalConnectDB := test_utils.MockConnectDB
			test_utils.MockConnectDB = func(connectionString string) (db.Querier, error) {
				return mockQuerier, nil
			}
			defer func() { test_utils.MockConnectDB = originalConnectDB }()

			err := cmd.RunRoleUpdate(ctx)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}

			mockQuerier.AssertExpectations(t)
		})
	}
}
